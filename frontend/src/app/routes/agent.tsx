import Lottie from 'lottie-react'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router'
import { useTheme } from 'next-themes'

import ThinkingLottie from '@/assets/thinking_2.json'
import ThinkingDarkMode from '@/assets/thinking_dark_mode.json'
import AgentBuild from '@/components/agent/agent-build'
import AgentSteps from '@/components/agent/agent-step'
import AgentTabs from '@/components/agent/agent-tab'
import AgentTasks from '@/components/agent/agent-task'
import ChatBox from '@/components/agent/chat-box'
import AgentHeader from '@/components/agent/header'
import RightSidebar from '@/components/right-sidebar'
import { sessionService } from '@/services/session.service'
import {
    selectActiveTab,
    selectSelectedBuildStep,
    selectVscodeUrl,
    selectIsSandboxIframeAwake,
    setSelectedFeature,
    useAppDispatch,
    useAppSelector,
    selectIsLoading
} from '@/state'
import { BUILD_STEP, ISession, TAB } from '@/typings/agent'
import <PERSON><PERSON><PERSON><PERSON> from '@/components/agent/agent-result'
import AgentPopoverDone from '@/components/agent/agent-popover-done'
import { useSocketIOContext } from '@/contexts/websocket-context'
import AwakeMeUpScreen from '@/components/agent/awake-me-up-screen'

function AgentPageContent() {
    const { sessionId } = useParams()
    const dispatch = useAppDispatch()
    const navigate = useNavigate()
    const theme = useTheme()
    const location = useLocation()

    const activeTab = useAppSelector(selectActiveTab)
    const vscodeUrl = useAppSelector(selectVscodeUrl)
    const selectedBuildStep = useAppSelector(selectSelectedBuildStep)
    const isSandboxIframeAwake = useAppSelector(selectIsSandboxIframeAwake)
    const [sessionData, setSessionData] = useState<ISession>()
    const [sessionError, setSessionError] = useState<string | null>(null)
    const [iframeKey, setIframeKey] = useState(0)
    const [isAwakeLoading, setIsAwakeLoading] = useState(false)
    const { socket } = useSocketIOContext()
    const isRunning = useAppSelector(selectIsLoading)

    const isShareMode = useMemo(
        () => location.pathname.includes('/share/'),
        [location.pathname]
    )

    const isE2bLink = (url: string): boolean => {
        try {
            const parsed = new URL(url)
            return (
                parsed.hostname.includes('e2b') ||
                parsed.hostname.includes('e2b-')
            )
        } catch {
            return false
        }
    }

    const handleAwakeClick = useCallback(() => {
        setIsAwakeLoading(true)
        if (socket?.connected) {
            socket.emit('chat_message', { type: 'awake_sandbox' })
        }
    }, [socket])

    useEffect(() => {
        if (activeTab === TAB.CODE) {
            setIframeKey((prev) => prev + 1)
            if (socket?.connected) {
                socket.emit('chat_message', { type: 'sandbox_status' })
            }
        }
    }, [activeTab, socket])

    useEffect(() => {
        if (isSandboxIframeAwake) {
            setIsAwakeLoading(false)
        }
    }, [isSandboxIframeAwake])

    useEffect(() => {
        let timeoutId: NodeJS.Timeout | undefined

        const fetchSession = async () => {
            if (sessionId) {
                try {
                    const data = await sessionService.getSession(sessionId)

                    if (!data?.name || data.name.trim() === '') {
                        // Retry after 5 seconds if name is null or empty
                        timeoutId = setTimeout(() => {
                            fetchSession()
                        }, 5000)
                    } else {
                        dispatch(setSelectedFeature(data.agent_type))
                        setSessionData(data)
                        setSessionError(null) // Clear any previous errors
                    }
                } catch (error: unknown) {
                    // Handle 404 errors specifically
                    if (
                        error &&
                        typeof error === 'object' &&
                        'response' in error
                    ) {
                        const axiosError = error as {
                            response: { status: number }
                        }
                        if (axiosError.response?.status === 404) {
                            setSessionError('404 - Session not found')
                        } else {
                            setSessionError('Failed to load session')
                        }
                    } else {
                        setSessionError('Failed to load session')
                    }
                    console.error('Error fetching session:', error)
                }
            }
        }

        fetchSession()

        return () => {
            if (timeoutId) {
                clearTimeout(timeoutId)
            }
        }
    }, [sessionId, dispatch])

    useEffect(() => {
        if (isSandboxIframeAwake) {
            setIframeKey((prev) => prev + 1)
        }
    }, [isSandboxIframeAwake])

    // Show error page if there's a session error
    if (sessionError) {
        return (
            <div className="flex h-screen items-center justify-center">
                <div className="text-center">
                    <h1 className="text-4xl font-semibold text-black dark:text-white mb-4">
                        {sessionError}
                    </h1>
                    <p className="text-lg text-gray-600 dark:text-gray-400 mb-6">
                        The session you&apos;re looking for doesn&apos;t exist
                        or has been deleted.
                    </p>
                    <button
                        onClick={() => navigate(-1)}
                        className="px-6 py-3 bg-firefly dark:bg-sky-blue text-sky-blue dark:text-black rounded-lg font-medium hover:opacity-80 transition-opacity"
                    >
                        Go Back
                    </button>
                </div>
            </div>
        )
    }

    return (
        <div className="flex h-screen">
            <div className="flex-1">
                <AgentHeader sessionData={sessionData} />
                <div className="flex !h-[calc(100vh-53px)]">
                    <div
                        className={`flex-1 flex items-center justify-center ${activeTab === TAB.BUILD && selectedBuildStep === BUILD_STEP.THINKING ? '' : 'hidden'}`}
                    >
                        {isRunning ? (
                            <div className="flex flex-col items-center justify-center">
                                <Lottie
                                    className="w-40"
                                    animationData={
                                        theme.theme === 'dark'
                                            ? ThinkingDarkMode
                                            : ThinkingLottie
                                    }
                                    loop={true}
                                />
                                <p className="text-[32px] pl-6 font-semibold  text-black dark:text-sky-blue">
                                    I’m thinking...
                                </p>
                            </div>
                        ) : (
                            <div className="flex-1" />
                        )}
                    </div>
                    <div
                        className={`flex-1 flex flex-col h-full relative ${activeTab === TAB.BUILD && selectedBuildStep === BUILD_STEP.THINKING ? 'hidden' : ''}`}
                    >
                        <AgentTabs />
                        <div className="flex-1">
                            <div
                                className={
                                    activeTab === TAB.BUILD
                                        ? 'h-full'
                                        : 'hidden h-full'
                                }
                            >
                                <div
                                    className={`flex flex-col items-center justify-between p-6 pb-8 h-full`}
                                >
                                    <AgentSteps />
                                    <div
                                        className={`flex flex-1 flex-col justify-between w-full ${selectedBuildStep === BUILD_STEP.PLAN ? '' : 'hidden'}`}
                                    >
                                        <AgentTasks className="flex-1" />
                                        <div />
                                    </div>
                                    <AgentBuild
                                        className={
                                            selectedBuildStep ===
                                            BUILD_STEP.BUILD
                                                ? ''
                                                : 'hidden'
                                        }
                                    />
                                </div>
                            </div>

                            <div
                                className={`h-full ${activeTab === TAB.CODE ? '' : 'hidden'}`}
                            >
                                {vscodeUrl &&
                                isE2bLink(vscodeUrl) &&
                                !isSandboxIframeAwake &&
                                !isRunning &&
                                !isShareMode ? (
                                    <AwakeMeUpScreen
                                        isLoading={isAwakeLoading}
                                        onAwakeClick={handleAwakeClick}
                                    />
                                ) : vscodeUrl ? (
                                    <iframe
                                        key={iframeKey}
                                        src={vscodeUrl}
                                        className="w-full h-full"
                                    />
                                ) : null}
                            </div>

                            <div
                                className={`h-full relative ${activeTab === TAB.RESULT ? '' : 'hidden'}`}
                            >
                                <AgentResult />
                                <div className="absolute bottom-8 right-4">
                                    <AgentPopoverDone />
                                </div>
                            </div>
                        </div>
                    </div>
                    <ChatBox />
                </div>
            </div>
            <RightSidebar />
        </div>
    )
}

export function AgentPage() {
    return <AgentPageContent />
}

export const Component = AgentPage
