import { useCallback, useEffect, useMemo, useState } from 'react'
import clsx from 'clsx'

import { Button } from '../ui/button'
import { Icon } from '../ui/icon'
import { Switch } from '../ui/switch'
import { INIT_TOOLS } from '@/constants/tool'
import { useAppDispatch, useAppSelector } from '@/state/store'
import {
    setCurrentSettingData,
    setIsSavingSetting,
    setToolSettings
} from '@/state/slice/settings'
import ConnectToolMCP from './connect-tool-mcp'
import MediaSetting from './media-setting'
import { ISetting } from '@/typings/agent'
import { toast } from 'sonner'
import { settingsService } from '@/services/settings.service'
import { IMcpSettings } from '@/typings/settings'

interface ToolSettingProps {
    className?: string
}

const ToolSetting = ({ className }: ToolSettingProps) => {
    const dispatch = useAppDispatch()
    const toolSettings = useAppSelector((state) => state.settings.toolSettings)
    const [isOpenConnectToolMCP, setOpenConnectToolMCP] = useState(false)
    const [isOpenMediaSetting, setOpenMediaSetting] = useState(false)
    const [mcpSettings, setMcpSettings] = useState<IMcpSettings[]>([])
    const [editingMcp, setEditingMcp] = useState<IMcpSettings | null>(null)

    const fetchMcpSettings = useCallback(async () => {
        try {
            const response = await settingsService.getMcpSettings()
            setMcpSettings(response.settings)
        } catch (error) {
            console.error('Failed to fetch MCP settings:', error)
        }
    }, [])

    useEffect(() => {
        fetchMcpSettings()
    }, [fetchMcpSettings])

    const tools = useMemo(() => {
        return INIT_TOOLS.map((tool) => {
            let isActive = false
            switch (tool.name) {
                case 'Deep Research':
                    isActive = toolSettings.deep_research
                    break
                case 'Design Document':
                    isActive = toolSettings.design_document
                    break
                case 'Media Generation':
                    isActive = toolSettings.media_generation
                    break
                case 'Browser':
                    isActive = toolSettings.browser
                    break
                case 'Review Agent':
                    isActive = toolSettings.enable_reviewer
                    break
                default:
                    isActive = tool.isActive
            }
            return { ...tool, isActive }
        })
    }, [toolSettings])

    const handleToggle = async (
        toolName: string,
        checked: boolean,
        mcpId?: string
    ) => {
        if (mcpId) {
            try {
                const mcpSetting = mcpSettings.find((s) => s.id === mcpId)
                if (mcpSetting) {
                    await settingsService.updateMcpSettings(mcpId, {
                        is_active: checked
                    })
                    setMcpSettings((prev) =>
                        prev.map((s) =>
                            s.id === mcpId ? { ...s, is_active: checked } : s
                        )
                    )
                }
            } catch (error) {
                toast.error('Failed to update MCP tool')
                console.error('Error updating MCP tool:', error)
            }
        } else {
            // Handle built-in tool toggle
            const newSettings = { ...toolSettings }
            switch (toolName) {
                case 'Deep Research':
                    newSettings.deep_research = checked
                    break
                case 'Design Document':
                    newSettings.design_document = checked
                    break
                case 'Media Generation':
                    newSettings.media_generation = checked
                    break
                case 'Browser':
                    newSettings.browser = checked
                    break
                case 'Review Agent':
                    newSettings.enable_reviewer = checked
                    break
            }
            dispatch(setToolSettings(newSettings))
        }
    }

    const handleOpenConnectToolMCP = () => {
        setEditingMcp(null) // Clear any editing state
        setOpenConnectToolMCP(true)
    }

    const handleEdit = (toolName: string, mcpId?: string) => {
        if (mcpId) {
            const mcpToEdit = mcpSettings.find((s) => s.id === mcpId)
            if (mcpToEdit) {
                setEditingMcp(mcpToEdit)
                setOpenConnectToolMCP(true)
            }
        } else if (toolName === 'Media Generation') {
            setOpenMediaSetting(true)
        }
    }

    const handleDeleteMcp = async (mcpId: string) => {
        try {
            await settingsService.deleteMcpSettings(mcpId)
            setMcpSettings((prev) => prev.filter((s) => s.id !== mcpId))
            toast.success('MCP tool disconnected')
        } catch (error) {
            toast.error('Failed to delete MCP tool')
            console.error('Error deleting MCP tool:', error)
        }
    }

    const saveConfig = async (settingData: ISetting) => {
        try {
            dispatch(setIsSavingSetting(true))

            // await settingsService.saveSettings(settingData)

            dispatch(setCurrentSettingData(settingData))
            setOpenMediaSetting(false)
            toast.success('Configuration saved successfully')
        } catch (error) {
            console.error('Error saving configuration:', error)
            toast.error('Failed to save configuration. Please try again.')
        } finally {
            dispatch(setIsSavingSetting(false))
        }
    }

    return (
        <div className={`flex flex-col justify-between h-full ${className}`}>
            <div className="space-y-4 w-full flex-1">
                <div>
                    <p className="text-lg font-semibold dark:text-white">
                        Magic Tools
                    </p>
                    <p className="mt-1 dark:text-white/[0.56] text-sm">
                        Modern & accessible for general users
                    </p>
                </div>
                {tools.map((tool) => (
                    <div
                        key={tool.name}
                        className={`h-[77px] flex items-center justify-between rounded-2xl ${tool.isActive ? 'border-2 border-firefly dark:border-sky-blue-2 bg-sky-blue dark:bg-sky-blue-2/20 p-[14px]' : 'bg-firefly/10 dark:bg-sky-blue-2/5 p-4'}`}
                    >
                        <div className="flex items-center gap-x-4">
                            <div
                                className={`${tool.isActive ? 'bg-firefly dark:bg-sky-blue-2' : 'bg-firefly/10 dark:bg-white/10'} rounded-full size-[46px] flex items-center justify-center`}
                            >
                                <Icon
                                    name={tool.icon}
                                    className={clsx('size-7', {
                                        'stroke-sky-blue-2 dark:stroke-black':
                                            tool.isActive && !tool.isFill,
                                        ' stroke-black dark:stroke-white':
                                            !tool.isActive && !tool.isFill,
                                        'fill-sky-blue-2 dark:fill-black':
                                            tool.isActive && tool.isFill,
                                        'fill-black dark:fill-white':
                                            !tool.isActive && tool.isFill
                                    })}
                                />
                            </div>
                            <div className="flex-1">
                                <p className="text-base font-semibold dark:text-white">
                                    {tool.name}
                                </p>
                                <p className="mt-1 dark:text-white text-sm">
                                    {tool.description}
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center gap-x-4">
                            {tool.isRequireKey && (
                                <Button
                                    className="p-0 size-6"
                                    onClick={() => handleEdit(tool.name)}
                                >
                                    <Icon name="edit-2" />
                                </Button>
                            )}
                            <Switch
                                checked={tool.isActive}
                                onCheckedChange={(checked: boolean) => {
                                    handleToggle(tool.name, checked)
                                }}
                            />
                        </div>
                    </div>
                ))}

                {/* MCP Connected Tools Section */}
                {mcpSettings.length > 0 && (
                    <>
                        <div className="mt-8">
                            <p className="text-lg font-semibold dark:text-white">
                                Connected MCP Tools
                            </p>
                            <p className="mt-1 dark:text-white/[0.56] text-sm">
                                External tools connected via MCP
                            </p>
                        </div>
                        {mcpSettings.map((mcp) => (
                            <div
                                key={mcp.id}
                                className={`h-[77px] flex items-center justify-between rounded-2xl ${
                                    mcp.is_active
                                        ? 'border-2 border-firefly dark:border-sky-blue-2 bg-sky-blue dark:bg-sky-blue-2/20 p-[14px]'
                                        : 'bg-firefly/10 dark:bg-sky-blue-2/5 p-4'
                                }`}
                            >
                                <div className="flex items-center gap-x-4">
                                    <div
                                        className={`${
                                            mcp.is_active
                                                ? 'bg-firefly dark:bg-sky-blue-2'
                                                : 'bg-firefly/10 dark:bg-white/10'
                                        } rounded-full size-[46px] flex items-center justify-center`}
                                    >
                                        <Icon
                                            name="link-2"
                                            className={clsx('size-7', {
                                                'fill-sky-blue-2 dark:fill-black':
                                                    mcp.is_active,
                                                'fill-black dark:fill-white':
                                                    !mcp.is_active
                                            })}
                                        />
                                    </div>
                                    <div>
                                        <p className="text-base font-semibold dark:text-white">
                                            {
                                                Object.keys(
                                                    mcp.mcp_config
                                                        ?.mcpServers || {}
                                                )[0]
                                            }
                                        </p>
                                        <p className="mt-1 dark:text-white text-sm">
                                            MCP Connected Tool
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-center gap-x-3">
                                    <Button
                                        className="p-0 size-6"
                                        onClick={() => handleEdit('', mcp.id)}
                                    >
                                        <Icon
                                            name="edit-2"
                                            className="fill-sky-blue-2 size-6"
                                        />
                                    </Button>
                                    <Button
                                        className="p-0 size-6"
                                        variant="ghost"
                                        onClick={() => handleDeleteMcp(mcp.id)}
                                    >
                                        <Icon
                                            name="trash"
                                            className="stroke-red-500 size-6"
                                        />
                                    </Button>
                                    <Switch
                                        checked={mcp.is_active ?? false}
                                        onCheckedChange={(checked: boolean) => {
                                            handleToggle('', checked, mcp.id)
                                        }}
                                    />
                                </div>
                            </div>
                        ))}
                    </>
                )}
            </div>
            <Button
                className="h-12 bg-firefly dark:bg-sky-blue text-sky-blue-2 dark:text-black text-base gap-x-[6px] rounded-xl"
                onClick={handleOpenConnectToolMCP}
            >
                <Icon
                    name="link-2"
                    className="fill-sky-blue-2 dark:fill-black size-[22px]"
                />
                Connect tools
            </Button>
            <ConnectToolMCP
                open={isOpenConnectToolMCP}
                onOpenChange={(open) => {
                    setOpenConnectToolMCP(open)
                    if (!open) {
                        setEditingMcp(null)
                        fetchMcpSettings()
                    }
                }}
                editingMcp={editingMcp}
            />
            <MediaSetting
                open={isOpenMediaSetting}
                onOpenChange={setOpenMediaSetting}
                onSaveConfig={saveConfig}
            />
        </div>
    )
}

export default ToolSetting
