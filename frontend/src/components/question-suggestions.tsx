import { Button } from './ui/button'

interface SuggestionsProps {
  hidden?: boolean
  onSelect: (text: string) => void
  suggestions?: string[]
}

const DEFAULT_SUGGESTIONS = [
  'Create a business plan for my startup',
  'Generate pitch deck with financial projections',
  'Build a landing page',
  'Create marketing strategy and content calendar',
  'Generate product demo video and script'
]

const Suggestions = ({ hidden, onSelect, suggestions = DEFAULT_SUGGESTIONS }: SuggestionsProps) => {
  if (hidden) return null
  return (
    <div className="flex items-center flex-wrap max-h-[50px] overflow-auto gap-x-2 gap-y-[6px]">
      {suggestions.map((item) => (
        <Button
          key={item}
          className="text-xs bg-grey px-2 py-[3px] h-[22px] rounded-full text-black"
          onClick={() => onSelect(item)}
        >
          {item}
        </Button>
      ))}
    </div>
  )
}

export default Suggestions

