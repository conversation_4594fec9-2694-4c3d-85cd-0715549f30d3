import { useRef, useState, useEffect } from 'react'
import clsx from 'clsx'
import { toast } from 'sonner'

import { Button } from '../ui/button'
import ChatMessage from './chat-message'
import { useWebSocketContext } from '@/contexts/websocket-context'
import {
    selectEditingMessage,
    selectIsAgentInitialized,
    selectMessages,
    selectSelectedModel,
    selectToolSettings,
    selectUploadedFiles,
    setAgentInitialized,
    setCompleted,
    setEditingMessage,
    setLoading,
    setMessages,
    setStopped,
    useAppDispatch,
    useAppSelector
} from '@/state'
import { useSessionManager } from '@/hooks/use-session-manager'
import { useParams } from 'react-router'
import { useAppEvents } from '@/hooks/use-app-events'
import { useQuestionHandlers } from '@/hooks/use-question-handlers'
import AgentFiles from './agent-files'

enum TAB {
    CHAT = 'chat',
    DESIGN = 'design',
    FILES = 'files'
}

interface ChatBoxProps {
    isShareMode?: boolean
}

const ChatBox = ({ isShareMode = false }: ChatBoxProps) => {
    const dispatch = useAppDispatch()
    const { sessionId } = useParams()

    const messagesEndRef = useRef<HTMLDivElement>(null)
    const { handleEvent, handleClickAction } = useAppEvents()

    const { isReplayMode, processAllEventsImmediately } = useSessionManager({
        handleEvent
    })

    // Reset isAgentInitialized whenever we're in a session
    useEffect(() => {
        if (sessionId) {
            dispatch(setAgentInitialized(false))
        }
    }, [sessionId, dispatch])

    const { socket, connectSocket, sendMessage } = useWebSocketContext()

    const { handleEnhancePrompt, handleQuestionSubmit, handleKeyDown } =
        useQuestionHandlers()

    const uploadedFiles = useAppSelector(selectUploadedFiles)
    const editingMessage = useAppSelector(selectEditingMessage)
    const messages = useAppSelector(selectMessages)
    const toolSettings = useAppSelector(selectToolSettings)
    const isAgentInitialized = useAppSelector(selectIsAgentInitialized)
    const selectedModel = useAppSelector(selectSelectedModel)

    const [activeTab, setActiveTab] = useState(TAB.CHAT)

    const handleCancelQuery = () => {
        if (!socket || !socket.connected) {
            toast.error('Socket.IO connection is not open.')
            return
        }

        // Send cancel message to the server
        sendMessage({
            type: 'cancel',
            content: {}
        })
        dispatch(setLoading(false))
        dispatch(setStopped(true))
    }

    const handleEditMessage = (newQuestion: string) => {
        if (!socket || !socket.connected) {
            toast.error('Socket.IO connection is not open. Please try again.')
            dispatch({ type: 'SET_LOADING', payload: false })
            return
        }

        sendMessage({
            type: 'edit_query',
            content: {
                text: newQuestion,
                files: uploadedFiles?.map((file) => `.${file}`)
            }
        })

        // Update the edited message and remove all subsequent messages
        const editIndex = messages.findIndex((m) => m.id === editingMessage?.id)

        if (editIndex >= 0) {
            const updatedMessages = [...messages.slice(0, editIndex + 1)]
            updatedMessages[editIndex] = {
                ...updatedMessages[editIndex],
                content: newQuestion
            }
            dispatch(setMessages(updatedMessages))
        }

        dispatch(setCompleted(false))
        dispatch(setStopped(false))
        dispatch(setLoading(true))
        dispatch(setEditingMessage(undefined))
    }

    const handleReviewResult = () => {
        if (!socket || !socket.connected) {
            toast.error('Socket.IO connection is not open. Please try again.')
            dispatch({ type: 'SET_LOADING', payload: false })
            return
        }
        const { thinking_tokens, ...tool_args } = toolSettings

        dispatch({ type: 'SET_LOADING', payload: true })
        dispatch({ type: 'SET_COMPLETED', payload: false })

        // Only send init_agent event if agent is not already initialized
        if (!isAgentInitialized) {
            sendMessage({
                type: 'init_agent',
                content: {
                    model_name: selectedModel,
                    tool_args,
                    thinking_tokens
                }
            })
        }

        // Find the last user message
        const userMessages = messages.filter((msg) => msg.role === 'user')
        const lastUserMessage =
            userMessages.length > 0
                ? userMessages[userMessages.length - 1].content
                : ''

        sendMessage({
            type: 'review_result',
            content: {
                user_input: lastUserMessage
            }
        })
    }

    return (
        <div className="h-full w-[480px] border-l border-neutral-200 dark:border-white/30">
            <div className="flex gap-x-2 items-center p-4">
                <Button
                    className={clsx(
                        'h-7 text-xs font-semibold px-4 rounded-full border border-sky-blue',
                        {
                            'bg-firefly border-firefly dark:border-sky-blue-2 dark:bg-sky-blue text-sky-blue-2 dark:text-black':
                                activeTab === TAB.CHAT,
                            'dark:border-sky-blue border-firefly dark:text-sky-blue':
                                activeTab !== TAB.CHAT
                        }
                    )}
                    onClick={() => setActiveTab(TAB.CHAT)}
                >
                    Chat
                </Button>
                <Button
                    className={clsx(
                        'h-7 text-xs font-semibold px-4 rounded-full border border-sky-blue',
                        {
                            'bg-firefly border-firefly dark:border-sky-blue-2 dark:bg-sky-blue text-sky-blue-2 dark:text-black':
                                activeTab === TAB.DESIGN,
                            'dark:border-sky-blue border-firefly dark:text-sky-blue':
                                activeTab !== TAB.DESIGN
                        }
                    )}
                    onClick={() => setActiveTab(TAB.DESIGN)}
                >
                    Design
                </Button>
                <Button
                    className={clsx(
                        'h-7 text-xs font-semibold px-4 rounded-full border border-sky-blue',
                        {
                            'bg-firefly border-firefly dark:border-sky-blue-2 dark:bg-sky-blue text-sky-blue-2 dark:text-black':
                                activeTab === TAB.FILES,
                            'dark:border-sky-blue border-firefly dark:text-sky-blue':
                                activeTab !== TAB.FILES
                        }
                    )}
                    onClick={() => setActiveTab(TAB.FILES)}
                >
                    All files
                </Button>
            </div>
            <div
                className={clsx(
                    'h-[calc(100vh-145px)] overflow-y-auto overflow-x-hidden',
                    {
                        hidden: activeTab !== TAB.CHAT
                    }
                )}
            >
                <ChatMessage
                    handleClickAction={handleClickAction}
                    isReplayMode={isReplayMode}
                    messagesEndRef={messagesEndRef}
                    setCurrentQuestion={(value) =>
                        dispatch({
                            type: 'SET_CURRENT_QUESTION',
                            payload: value
                        })
                    }
                    handleKeyDown={handleKeyDown}
                    handleQuestionSubmit={handleQuestionSubmit}
                    handleEnhancePrompt={handleEnhancePrompt}
                    handleCancel={handleCancelQuery}
                    handleEditMessage={handleEditMessage}
                    processAllEventsImmediately={processAllEventsImmediately}
                    connectWebSocket={connectSocket}
                    handleReviewSession={handleReviewResult}
                    isShareMode={isShareMode}
                />
            </div>
            <div
                className={clsx(
                    'h-[calc(100vh-145px)] overflow-y-auto overflow-x-hidden',
                    {
                        hidden: activeTab !== TAB.FILES
                    }
                )}
            >
                <AgentFiles
                    isActive={activeTab === TAB.FILES}
                    sessionId={sessionId}
                />
            </div>
        </div>
    )
}

export default ChatBox
