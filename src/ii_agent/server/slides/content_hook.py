"""Slide content hook for processing slide tool results."""

import logging
from typing import Optional
from copy import deepcopy

from ii_agent.core.event import Realtime<PERSON>vent, EventType
from ii_agent.core.event_hooks import EventHook
from ii_agent.server.slides.content_processor import SlideContentProcessor
from ii_agent.storage.base import BaseStorage
from ii_agent.sandbox import IISandbox

logger = logging.getLogger(__name__)


class SlideContentHook(EventHook):
    """Hook that processes slide tool results to replace local paths with permanent URLs."""

    def __init__(self, storage: BaseStorage, sandbox: IISandbox):
        self.storage = storage
        self.sandbox = sandbox
        self.content_processor = SlideContentProcessor(storage, sandbox)

    def should_process(self, event: RealtimeEvent) -> bool:
        """Check if this is a slide tool result that should be processed."""
        if event.type != EventType.TOOL_RESULT:
            return False

        tool_name = event.content.get("tool_name", "")
        return tool_name in ["SlideWrite", "SlideEdit"]

    async def process_event(self, event: RealtimeEvent) -> Optional[RealtimeEvent]:
        """Process slide tool result to replace local paths with permanent URLs."""
        try:
            # Deep copy to avoid modifying original event
            processed_event = RealtimeEvent(
                type=event.type, content=deepcopy(event.content)
            )

            tool_name = processed_event.content.get("tool_name", "")
            logger.debug(f"Processing slide tool result for: {tool_name}")

            # Extract HTML content and filepath from different possible locations
            html_content = None
            slide_file_path = None
            content_location = None

            logger.debug(f"Event content: {processed_event.content}")

            # Check user_display_content for SlideWrite
            user_display = processed_event.content.get("result")
            if isinstance(user_display, dict) and "content" in user_display:
                html_content = user_display["content"]
                slide_file_path = user_display.get("filepath")
                content_location = "result.content"
            elif isinstance(user_display, list) and len(user_display) > 0:
                # For SlideEdit - check first item
                first_item = user_display[0]
                if isinstance(first_item, dict) and "new_content" in first_item:
                    html_content = first_item["new_content"]
                    slide_file_path = first_item.get("filepath")
                    content_location = "result[0].new_content"

            if not html_content:
                logger.warning(f"No HTML content found in {tool_name} tool result")
                return processed_event

            if not slide_file_path:
                logger.warning(f"No filepath found in {tool_name} tool result")
                return processed_event

            # Process the HTML content
            processed_html = await self.content_processor.process_html_content(
                html_content, slide_file_path
            )

            # Update the content in the event
            if content_location == "result.content":
                processed_event.content["result"]["content"] = processed_html
            elif content_location == "result[0].new_content":
                processed_event.content["result"][0]["new_content"] = processed_html

            logger.debug(f"Successfully processed slide content for {tool_name}")
            return processed_event

        except Exception as e:
            logger.error(f"Error processing slide content hook: {e}")
            return event  # Return original event on error
