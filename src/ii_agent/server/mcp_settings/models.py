"""MCP settings management Pydantic models."""

from fastmcp.mcp_config import RemoteMCPServer, StdioMCPServer
from pydantic import BaseModel, Field
from typing import Optional, Dict, List


class MCPServersConfig(BaseModel):
    """Configuration for all MCP servers."""

    mcpServers: Dict[str, StdioMCPServer | RemoteMCPServer] = Field(
        default_factory=dict, description="Map of server names to their configurations"
    )


class MCPSettingCreate(BaseModel):
    """Model for creating/updating MCP settings."""

    mcp_config: MCPServersConfig = Field(..., description="MCP configuration object")


class MCPSettingUpdate(BaseModel):
    """Model for updating existing MCP settings."""

    mcp_config: Optional[MCPServersConfig] = Field(
        None, description="MCP configuration object"
    )
    is_active: Optional[bool] = Field(
        None, description="Whether the MCP setting is active"
    )


class MCPSettingInfo(BaseModel):
    """Model for MCP setting information."""

    id: str
    mcp_config: MCPServersConfig
    is_active: bool
    created_at: str
    updated_at: Optional[str] = None


class MCPSettingList(BaseModel):
    """Model for MCP setting list response."""

    settings: List[MCPSettingInfo]

    def get_by_id(self, setting_id: str) -> Optional[MCPSettingInfo]:
        """Get MCP setting by ID."""
        return next(
            (setting for setting in self.settings if setting.id == setting_id),
            None,
        )

    def get_combined_active_config(self) -> MCPServersConfig:
        """Combine all active MCP settings into a single configuration.

        Each active MCP setting contributes its servers to the combined config.
        If multiple settings have servers with the same name, the last one wins.

        Returns:
            MCPServersConfig: Combined configuration with all active MCP servers
        """
        combined_servers: Dict[str, StdioMCPServer | RemoteMCPServer] = {}

        # Iterate through all active settings
        for setting in self.settings:
            if (
                setting.is_active
                and setting.mcp_config
                and setting.mcp_config.mcpServers
            ):
                # Add or update servers from this setting
                for server_name, server_config in setting.mcp_config.mcpServers.items():
                    combined_servers[server_name] = server_config

        return MCPServersConfig(mcpServers=combined_servers)

    def get_combined_active_config_dict(
        self,
    ) -> Dict[str, Dict[str, StdioMCPServer | RemoteMCPServer]]:
        """Get combined active MCP configuration as a dictionary.

        Returns:
            Dict: Combined configuration in the format {"mcpServers": {...}}
        """
        combined_config = self.get_combined_active_config()
        return {"mcpServers": combined_config.mcpServers}
