"""Simplified sandbox service that communicates with the standalone sandbox server."""

import uuid
import logging
from typing import Optional

from ii_agent.sandbox import IISandbox
from ii_agent.core.config.ii_agent_config import IIAgentConfig
from ii_agent.db.manager import Sessions, get_db
from ii_agent.server.mcp_settings.service import list_mcp_settings
from ii_tool.mcp.client import MCPClient


logger = logging.getLogger(__name__)


class SandboxService:
    """Simplified sandbox service that delegates to the sandbox server."""
    
    def __init__(self, config: IIAgentConfig):
        self.config = config
        self.tool_server_url = config.tool_server_url
        self.sandbox_server_url = config.sandbox_server_url
        self.sandbox_template_id = config.sandbox_template_id
    
    async def get_sandbox_by_session(self, session_uuid: uuid.UUID) -> IISandbox: 
        """Ensure a sandbox exists for the given session ID."""
        existing_session = await Sessions.get_session_by_id(session_uuid)
        if not existing_session:
            # Session not found exception
            raise Exception(f"Session {session_uuid} not found")
        
        if not await Sessions.session_has_sandbox(session_uuid):
            # Create new sandbox, sandbox service will handle the creation
            sandbox = IISandbox(str(existing_session.sandbox_id), self.sandbox_server_url, str(existing_session.user_id))
            await sandbox.create(self.sandbox_template_id)
            
            # Get MCP settings for the user
            mcp_config = await self._get_user_mcp_config(str(existing_session.user_id))
            # Update session with sandbox ID
            await Sessions.update_sandbox_id(
                session_uuid=session_uuid,
                sandbox_id=sandbox.sandbox_id
            )
            await self._set_tool_server_url(sandbox)
            await self._register_user_mcp_servers(str(existing_session.user_id), sandbox)
            await sandbox.write_file(str(session_uuid), "credential.json")
            logger.info(f"Created new session {session_uuid} with sandbox {sandbox.sandbox_id}")
            return sandbox
        else:
            # Connect to existing sandbox
            sandbox = IISandbox(str(existing_session.sandbox_id), self.sandbox_server_url, str(existing_session.user_id))
            await sandbox.connect()
            return sandbox

    async def get_sandbox_status_by_session(self, session_id: uuid.UUID) -> str:
        """Get sandbox status by session ID."""
        session = await Sessions.get_session_by_id(session_id)
        if not session or not str(session.sandbox_id):
            return "not initialized"

        sandbox = IISandbox(str(session.sandbox_id), self.sandbox_server_url, str(session.user_id))
        return await sandbox.status
    
    async def wake_up_sandbox_by_session(self, session_id: uuid.UUID):
        """Wake up a paused sandbox by session ID."""
        session = await Sessions.get_session_by_id(session_id)
        if not session or not str(session.sandbox_id):
            raise Exception(f"Session {session_id} not found or not initialized")
        
        sandbox = IISandbox(str(session.sandbox_id), self.sandbox_server_url, str(session.user_id))
        await sandbox.connect()
    
    async def cleanup_sandbox_for_session(self, session_uuid: uuid.UUID, time_til_clean_up: int = 15 * 60):
        """Schedule a timeout for a session's sandbox."""
        existing_session = await Sessions.get_session_by_id(session_uuid)
        if not existing_session:
            raise Exception(f"Session {session_uuid} not found")
        if not await Sessions.session_has_sandbox(session_uuid):
            raise Exception("Sandbox not initialized")
        sandbox = IISandbox(str(existing_session.sandbox_id), self.sandbox_server_url, str(existing_session.user_id))
        await sandbox.schedule_timeout(time_til_clean_up)
    
    async def _get_user_mcp_config(self, user_id: str) -> Optional[dict]:
        """Get combined MCP configuration for a user."""
        try:
            async with get_db() as db_session:
                mcp_settings = await list_mcp_settings(
                    db_session=db_session,
                    user_id=user_id,
                    only_active=True
                )
            
            if not mcp_settings.settings:
                return None
            
            # Get combined configuration
            combined_config = mcp_settings.get_combined_active_config()
            
            if not combined_config.mcpServers:
                return None
            
            # Convert to dict for registration
            return combined_config.model_dump(exclude_none=True)
            
        except Exception as e:
            logger.error(f"Failed to get MCP config for user {user_id}: {e}")
            return None
    
    async def _set_tool_server_url(self, sandbox: IISandbox):
        """Set the tool server url for the sandbox."""
        mcp_port = self.config.mcp_port
        sandbox_url = await sandbox.expose_port(mcp_port)
        async with MCPClient(sandbox_url) as client:
            await client.set_tool_server_url(self.config.tool_server_url)
        return True

    async def _register_user_mcp_servers(self, user_id: str, sandbox: IISandbox) -> bool:
        """Register user's MCP servers with the sandbox.

        Returns:
            bool: True if registration succeeded or no servers to register, False on error
        """
        if not user_id or not sandbox.sandbox_id:
            logger.info("No user_id or sandbox available for MCP registration")
            return True

        # Get sandbox URL for MCP registration
        mcp_port = self.config.mcp_port
        sandbox_url = await sandbox.expose_port(mcp_port)

        # Query active MCP settings for user
        async with get_db() as db_session:
            mcp_settings = await list_mcp_settings(
                db_session=db_session, user_id=user_id, only_active=True
            )

        if not mcp_settings.settings:
            logger.info(
                f"No active MCP servers to register for user {user_id}"
            )
            return True  # No MCP servers to register

        # Get combined configuration using the new method
        combined_config = mcp_settings.get_combined_active_config()

        # Only register if we have servers to register
        if not combined_config.mcpServers:
            logger.info(
                f"No MCP servers found in active settings for user {user_id}"
            )
            return True

        # Convert to dict for registration
        config_dict = combined_config.model_dump(exclude_none=True)

        # Register with sandbox using MCPClient
        async with MCPClient(sandbox_url) as client:
            await client.register_custom_mcp(config_dict)
            tools = await client.list_tools()
            logger.info(
                f"Registered {len(tools)} MCP tools for user {user_id}"
            )

            return True