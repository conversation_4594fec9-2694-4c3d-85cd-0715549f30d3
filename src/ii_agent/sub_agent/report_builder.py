import asyncio
import copy
import json
import logging
import re
from enum import Enum
from pydantic import BaseModel, SecretStr
from datetime import datetime, timezone
from typing import List

from ii_agent.core.config.llm_config import LLMConfig
from ii_agent.core.event import EventType, RealtimeEvent
from ii_agent.core.event_stream import EventStream
from ii_agent.llm.anthropic import AnthropicDirectClient
from ii_agent.llm.base import LLMClient, TextPrompt, TextResult, ThinkingBlock, ToolCall, ToolFormattedResult, ToolResult

from ii_agent.controller.state import State
from ii_agent.llm.openai import OpenAIDirectClient

class ReportConfig(BaseModel):

    def generate_introduction_messages(
        self, query: str
    ) -> str:
        return f"""
Using the above latest information as well as the thoughts and reasoning of the researcher, Prepare a detailed report introduction on the topic -- {query}.
- The introduction should be succinct, well-structured, informative with markdown syntax.
- As this introduction will be part of a larger report, do NOT include any other sections, which are generally present in a report.
- The introduction should be preceded by an H1 heading with a suitable topic for the entire report.
- For all mathematical equations and formulas, use LaTeX syntax wrapped in double dollar signs ($$) for display equations and single dollar signs ($) for inline math to ensure proper rendering with pandoc xelatex. For example: $$E = mc^2$$ for display or $x^2 + y^2 = z^2$ for inline.
- You must use in-text citation references in apa format and make it with markdown hyperlink placed at the end of the sentence or paragraph that references them like this: ([in-text citation](url)), replace in-text citation with the actual citation from the report.
Assume that the current date is {datetime.now(timezone.utc).strftime("%B %d, %Y")} if required.
- The output must be written in the same language as the main topic.
"""
    def generate_subtopics_messages(
        self, query: str, introduction: str
    ) -> str:
        return f"""
Above this message is the research draft of a world-renowned researcher, including sources, thinking thoughts and the final research report. Althought the report might not be comprehensive enough. Your task is structure
the above draft into a structured sub topics that will be used to generate a comprehensive report.
The introduction is:
{introduction}

Provided the main topic:

{query}

- Construct a list of subtopics which indicate the headers of a report document to be generated on the task. 
- There should NOT be any duplicate subtopics.
- Limit the number of subtopics from 2 to 7
- Finally order the subtopics by their tasks, in a relevant and meaningful order which is presentable in a detailed report
- The subtopics must be written in the same language as the main topic.

"IMPORTANT!":
- Every subtopic MUST be relevant to the main topic and provided research data ONLY!

Format requirements:
- Output a list of subtopics seperated by a comma. For example: "Subtopic 1, Subtopic 2, Subtopic 3". "Introduction, Subtopic 1, Subtopic 2, Subtopic 3, Conclusion"
- Do not include any other text in your response just the list of subtopics seperated by a comma.
            """

    def generate_subtopic_report_messages(
        self,
        content_from_previous_subtopics: str,
        subtopics: List[str],
        current_subtopic: str,
        query: str,
    ) -> str:
        return f"""
        Based on the research above as well as the thoughts and reasoning of the researcher, you are constructing a detailed report on the subtopics: {subtopics} to answer the main topic: {query}. You are currently writing the report on the subtopic: {current_subtopic}.

Content from previous subtopics:
{content_from_previous_subtopics}

Content Focus:
- The report should focus on answering the question, be well-structured, informative, in-depth, and include facts and numbers if available.
- Use markdown syntax and follow the APA format.
- When presenting data, comparisons, or structured information, use markdown tables to enhance readability.

IMPORTANT:Content and Sections Uniqueness:
- This part of the instructions is crucial to ensure the content is unique and does not overlap with existing reports.
- Carefully review the existing headers and existing written contents provided below before writing any new subsections.
- Prevent any content that is already covered in the existing written contents.
- Do not use any of the existing headers as the new subsection headers.
- Do not repeat any information already covered in the existing written contents or closely related variations to avoid duplicates.
- If you have nested subsections, ensure they are unique and not covered in the existing written contents.
- For all mathematical equations and formulas, you MUST use LaTeX syntax with double dollar signs ($$) for display equations and single dollar signs ($) for inline math. This ensures proper rendering with pandoc xelatex.
- Avoid complex LaTeX environments like \\begin and \\end. Use $$ notation instead.
- Ensure that your content is entirely new and does not overlap with any information already covered in the previous subtopic reports.
- Your report must be written in the same language as the main topic and subtopic

- All the available topics are:

    {subtopics}

"Structure and Formatting":
- As this sub-report will be part of a larger report, include only the main body divided into suitable subtopics without any introduction or conclusion section.

- You MUST include markdown hyperlinks to relevant source URLs wherever referenced in the report, for example:

    ### Section Header
    
    This is a sample text ([in-text citation](url)).
    IMPORTANT: Replace in-text citation with the actual citation from the report.

- Use H2 for the main subtopic header (##) and H3 for subsections (###).
- Use smaller Markdown headers (e.g., H2 or H3) for content structure, avoiding the largest header (H1) as it will be used for the larger report's heading.
- Organize your content into distinct sections that build on top of each other and complement but do not overlap with existing subtopic reports.
- When adding similar or identical subsections to your report, you should clearly indicate the differences between and the new content and the existing written content from previous subtopic reports. For example:

    ### New header (similar to existing header)

    While the previous section discussed [topic A], this section will explore [topic B]."

"Date":
Assume the current date is {datetime.now(timezone.utc).strftime("%B %d, %Y")} if required.

"IMPORTANT!":
- You must focus on your designated subtopic only. Each subtopic should build on the previous subtopic but do not repeat the same key information.
- The focus MUST be on the main topic! You MUST Leave out any information un-related to it!
- Must NOT have any introduction, conclusion, summary or reference section.
- You MUST use in-text citation references in APA format and make it with markdown hyperlink placed at the end of the sentence or paragraph that references them like this: ([in-text citation](url)), replace in-text citation with the actual citation from the report.
- You MUST mention the difference between the existing content and the new content in the report if you are adding the similar or same subsections wherever necessary.

Do NOT add a conclusion section.
"""

    def get_generate_report_system_prompt(self) -> str:
        return """
Above is this message is the research draft of a world-renowned researcher, including sources, thinking thoughts and the final research report. Althought the report might not be comprehensive enough. Your task is structure
the above draft into a comprehensive report of the following question:

{question}

You are a specialized document structuring assistant from II AI. Your task is to analyze a main topic and supporting research data, then generate a comprehensive report.
 
All the report should be focused on answering the original question, should be well structured, informative, in-depth, and comprehensive, with facts and numbers if available and at least 1000 words.
You should strive to write the report as long as you can using all relevant and necessary information provided.

Please follow all of the following guidelines in your report:
- You MUST determine your own concrete and valid opinion based on the given information. Do NOT defer to general and meaningless conclusions.
- You MUST write the report with markdown syntax and apa format.
- For all mathematical equations, formulas, and expressions, use LaTeX syntax with $$ for display equations and $ for inline math to ensure compatibility with pandoc xelatex.
- You MUST prioritize the relevance, reliability, and significance of the sources you use. Choose trusted sources over less reliable ones.
- You must also prioritize new articles over older articles if the source can be trusted.
- Use in-text citation references in apa format and make it with markdown hyperlink placed at the end of the sentence or paragraph that references them like this: ([in-text citation](url)), replace in-text citation with the actual citation from the report.
- For all references, use the exact url as provided in the visited URLs sections.
- The report language must be written in the same language as the main topic

You MUST write all used source urls at the end of the report as references, and make sure to not add duplicated sources, but only one reference for each.
Additionally, you MUST include hyperlinks to the relevant URLs wherever they are referenced in the report: 

eg: Author, A. A. (Year, Month Date). Title of web page. Website Name. [website](url)

The report should include:

MAIN CONTENT:
- Comprehensive analysis supported by search data
- Relevant statistics, metrics, or data points when available
- Information presented in appropriate formats (tables, lists, paragraphs) based on content type
- Evaluation of source reliability and information quality
- Key insights that directly address the original question
- Any limitations in the search or areas requiring further investigation
- Citations for all referenced information

VISUAL PRESENTATION:
- EXACTLY one well-structured table to present key data clearly
- Use consistent formatting with clear headers and aligned columns
- Include explanatory notes below the table when necessary
- Format data appropriately (correct units, significant figures, etc.) in markdown format
- For any mathematical expressions in tables or data, use LaTeX syntax with $ for inline math

FINAL SYNTHESIS:
- Key findings synthesis
- Evidence-based comprehensive conclusions
- Clear connection between findings and the original question

Every section must be directly relevant to the main topic and supported by the provided research data only.
The structure should be well-organized but natural, avoiding formulaic headings or numbered sections.
The response format is in well markdown.
IMPORTANT: All mathematical equations must use LaTeX syntax with $$ for display equations and $ for inline math for pandoc xelatex compatibility.

The report begins now. Do not include your own thoughts or comments. A well written report will be rewarded with $1,000,000.
"""
    def generate_references_messages(self) -> str:
        return """
Above is the report of a world-renowned researcher, including sources, thinking thoughts and the final research report.
You are a specialized document structuring assistant from II AI. Your task is to analyze the report and return a list of all unique sources (URLs) referenced in the report, formatted in APA style with markdown hyperlinks.
Return a list of all unique sources (URLs) referenced in the report, formatted in APA style with markdown hyperlinks.
Each reference should include the author (if available), publication date (if available), title, website name, and the exact URL.
Do not include duplicate URLs. The references should be written in the same language as the report.
Only use URLs that were actually cited in the main content. Format the list as markdown, with each reference on a new line.
"""

    def generate_website_messages(self) -> str:
        return f"""

{{report}}

Above is the report of a world-renowned researcher, including sources, thinking thoughts and the final research report. Your task is to 
write the report in a stunning css (use tailwindcss), html website that contains all the information in a very readable format,
add a table of contents that navigate to the relevant sections.
- Keep LaTeX equations in their original $$ or $ delimiters for MathJax to render them properly. The subtopics includes:

{{subtopics}}

And the main topic is:

{{main_topic}}

The website begins now. You should only output html code as any other text will make the html invalid. You will be rewarded with $1,000,000 if you create a stunning website that includes all the information in the report as well as functional links and table of contents.


BEGIN CODING HERE:
"""

# Create a singleton config instance

class ReportType(Enum):
    BASIC = "basic"
    ADVANCED = "advanced"


class Subtopics(BaseModel):
    subtopics: List[str]


class ReportBuilder:
    def __init__(
        self, client: LLMClient, event_stream: EventStream
    ):
        self.client = client
        self.config = ReportConfig()
        self.event_stream = event_stream
    
    def extract_valid_urls_from_state(self, state: State) -> List[str]:
        """Extract all valid URLs from the state's tool outputs."""
        urls = set()
        url_pattern = r'https?://[^\s\)\]\>"]+(?=[\s\)\]\>"]*(?:\s|$|[\)\]\>"]))'
        
        messages = state.get_messages_for_llm()
        for message_list in messages:
            for message in message_list:
                # Check tool outputs for URLs
                if (isinstance(message, ToolFormattedResult) or isinstance(message, ToolResult)) and message.tool_output:
                    output_str = str(message.tool_output)
                elif isinstance(message, TextResult) and message.text:
                    output_str = message.text
                elif isinstance(message, ThinkingBlock) and message.thinking:
                    output_str = message.thinking
                elif isinstance(message, ToolCall) and message.tool_input:
                    output_str = str(message.tool_input)
                else:
                    continue
                found_urls = re.findall(url_pattern, output_str, re.IGNORECASE)
                for url in found_urls:
                    # Clean up URL (remove trailing punctuation)
                    cleaned_url = re.sub(r'[.,;!?\'")\]]+$', '', url)
                    if cleaned_url.startswith(('http://', 'https://')):
                        urls.add(cleaned_url)
        
        return sorted(list(urls))
    
    def create_reference_constraint_prompt(self, valid_urls: List[str]) -> str:
        """Create a prompt constraint that limits references to valid URLs only."""
        if not valid_urls:
            return "\n\nIMPORTANT: No valid reference URLs were found in the research data. Do not include any external URL references in your report."
        
        urls_text = "\n".join([f"- {url}" for url in valid_urls])
        return f"""

IMPORTANT REFERENCE CONSTRAINT:
You MUST only use references from the following list of URLs that were actually visited and contain verified information:

{urls_text}

- You are STRICTLY PROHIBITED from creating, inventing, or using any URLs that are not in this list
- Every citation and reference MUST use one of the URLs from this exact list
- If you cannot find a suitable reference from this list, do not include a citation rather than making one up
- When citing, use the exact URL as provided in this list without any modifications"""

    async def build(
        self, query: str, state: State, report_type: ReportType = ReportType.BASIC
    ) -> str:

        if report_type == ReportType.BASIC:
            return await self.generate_report_stream(query, state)
        elif report_type == ReportType.ADVANCED:
            return await self.generate_advance_report_stream(query, state)
        else:
            raise ValueError(f"Invalid report type: {report_type}")

    async def generate_report_stream(
        self, query: str, state: State
    ) -> str:
        """Generate a streaming report using the OpenAI API."""

        try:
            messages = copy.deepcopy(state.get_messages_for_llm())
            
            # Extract valid URLs and add reference constraint
            valid_urls = self.extract_valid_urls_from_state(state)
            reference_constraint = self.create_reference_constraint_prompt(valid_urls)
            
            generate_instruction = self.config.get_generate_report_system_prompt().format(question=query) + reference_constraint
            messages.append([TextPrompt(text=generate_instruction)])
            contents, _ = await self.client.agenerate(
                messages=messages,
                max_tokens=8192,
                tool_choice={"type": "none"},
                system_prompt=generate_instruction,
            )
            full_content = ""
            for content in contents:
                if (isinstance(content, TextResult)):
                    full_content += content.text
            return full_content
        except Exception as e:
            logging.error("Error generating streaming report: %s", str(e))
            raise

    async def generate_advance_report_stream(
        self,
        query: str,
        state: State,
    ) -> str:
        try:
            introduction = await self._generate_introduction_stream(query, state)
            # Quick fix to show result in the frontend
            await self.event_stream.add_event(RealtimeEvent(type=EventType.AGENT_RESPONSE, content={"text": introduction}))
            subtopics = await self._generate_subtopics_stream(query, state, introduction)
            content_from_previous_subtopics = introduction
            for subtopic in subtopics:
                subtopic_content = await self._generate_subtopic_report_stream(
                    query,
                    state,
                    subtopic,
                    content_from_previous_subtopics,
                    subtopics,
                )
                await self.event_stream.add_event(RealtimeEvent(type=EventType.AGENT_RESPONSE, content={"text": subtopic_content}))
                content_from_previous_subtopics += f"\n\n{subtopic_content}"
            references = await self._generate_references_stream(content_from_previous_subtopics, state)
            await self.event_stream.add_event(RealtimeEvent(type=EventType.AGENT_RESPONSE, content={"text": references}))
            full_report = content_from_previous_subtopics + "\n\n" + references
            return full_report

        except Exception as e:
            logging.error("Error generating advance report: %s", str(e))
            raise

    async def _generate_introduction_stream(
        self, query: str, state: State
    ) -> str:
        try:
            # Extract valid URLs and add reference constraint
            valid_urls = self.extract_valid_urls_from_state(state)
            reference_constraint = self.create_reference_constraint_prompt(valid_urls)
            
            instruction = self.config.generate_introduction_messages(query) + reference_constraint
            messages = copy.deepcopy(state.get_messages_for_llm())
            messages.append([TextPrompt(text=instruction)])

            contents, _ =  await self.client.agenerate(
                messages=messages,
                max_tokens=8192,
                tool_choice={"type": "none"},
                system_prompt=instruction,
            )
            introduction = ""
            for content in contents:
                if (isinstance(content, TextResult)):
                    introduction += content.text
            print(f"Introduction: {introduction}")
            return introduction
        except Exception as e:
            logging.error("Error generating introduction: %s", str(e))
            raise

    async def _generate_subtopics_stream(self, query: str, state: State, introduction: str) -> list[str]:
        try:
            instruction = self.config.generate_subtopics_messages(query, introduction)
            messages = copy.deepcopy(state.get_messages_for_llm())
            messages.append([TextPrompt(text=instruction)])

            contents, _ =  await self.client.agenerate(
                messages=messages,
                max_tokens=8192,
                tool_choice={"type": "none"},
                system_prompt=instruction,
            )
            for content in contents:
                if (isinstance(content, TextResult)):
                    self.subtopics = content.text.split(",")
                    return content.text.split(",")
            else:
                raise ValueError("subtopics response is not a text result")
        except Exception as e:
            logging.error("Error generating subtopics: %s", str(e))
            raise

    async def _generate_subtopic_report_stream(
        self,
        query: str,
        state: State,
        current_subtopic: str,
        content_from_previous_subtopics: str,
        subtopics: list[str],
    ) -> str:
        try:
            # Extract valid URLs and add reference constraint
            valid_urls = self.extract_valid_urls_from_state(state)
            reference_constraint = self.create_reference_constraint_prompt(valid_urls)
            
            instruction = self.config.generate_subtopic_report_messages(
                content_from_previous_subtopics,
                subtopics,
                current_subtopic,
                query,
            ) + reference_constraint
            logging.info(f"Generating subtopic report: {current_subtopic}")
            messages = copy.deepcopy(state.get_messages_for_llm())
            messages.append([TextPrompt(text=instruction)])

            contents, _ =  await self.client.agenerate(
                messages=messages,
                max_tokens=8192,
                tool_choice={"type": "none"},
                system_prompt=instruction,
            )
            subtopic_report = ""
            for content in contents:
                if (isinstance(content, TextResult)):
                    subtopic_report += content.text
            return subtopic_report
        except Exception as e:
            logging.error("Error generating subtopic report: %s", str(e))
            raise

    async def _generate_references_stream(
        self,
        report: str,
        state: State,
    ) -> str:
        """Generate the references section of the report with streaming.

        Args:
            state: The state containing the research trace and tool history

        Returns:
            str: The references section text

        Raises:
            Exception: If references generation fails
        """
        try:
            valid_urls = self.extract_valid_urls_from_state(state)
            reference_constraint = self.create_reference_constraint_prompt(valid_urls)
            instruction = self.config.generate_references_messages()
            messages = [[TextResult(text=report)], [TextPrompt(text=instruction + reference_constraint)]]
            contents, _ =  await self.client.agenerate(
                messages=messages,
                max_tokens=8192,
                tool_choice={"type": "none"},
                system_prompt=instruction,
            )
            references = ""
            for content in contents:
                if (isinstance(content, TextResult)):
                    references += content.text
            return references
        except Exception as e:
            logging.error("Error generating references: %s", str(e))
            return ""

    
    async def build_website(self, report: str, query: str) -> str:
        try:
            instruction = self.config.generate_website_messages().format(report=report, subtopics=self.subtopics, main_topic=query)
            messages = [[TextPrompt(text=instruction)]]
            contents, _ =  await self.client.agenerate(
                messages=messages,
                max_tokens=8192,
                tool_choice={"type": "none"},
                system_prompt=instruction,
            )
            website = ""
            for content in contents:
                if (isinstance(content, TextResult)):
                    website += content.text
            return website
        except Exception as e:
            logging.error("Error generating website: %s", str(e))
            raise
