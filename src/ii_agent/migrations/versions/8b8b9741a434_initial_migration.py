"""Initial migration

Revision ID: 8b8b9741a434
Revises:
Create Date: 2025-09-11 14:16:38.950939

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = "8b8b9741a434"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "users",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("email", sa.String(), nullable=False),
        sa.Column("password_hash", sa.String(), nullable=True),
        sa.Column("first_name", sa.String(), nullable=True),
        sa.Column("last_name", sa.String(), nullable=True),
        sa.Column("avatar", sa.String(), nullable=True),
        sa.Column("role", sa.String(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=True),
        sa.Column("email_verified", sa.Boolean(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("last_login_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("metadata", sqlite.JSON(), nullable=True),
        sa.Column("login_provider", sa.String(), nullable=True),
        sa.Column("organization", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("email"),
    )
    op.create_index("idx_users_email", "users", ["email"], unique=False)
    op.create_table(
        "llm_settings",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("user_id", sa.String(), nullable=False),
        sa.Column("model", sa.String(), nullable=False),
        sa.Column("api_type", sa.String(), nullable=False),
        sa.Column("encrypted_api_key", sa.String(), nullable=True),
        sa.Column("base_url", sa.String(), nullable=True),
        sa.Column("max_retries", sa.BigInteger(), nullable=True),
        sa.Column("max_message_chars", sa.BigInteger(), nullable=True),
        sa.Column("temperature", sa.Float(), nullable=True),
        sa.Column("thinking_tokens", sa.BigInteger(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("metadata", sqlite.JSON(), nullable=True),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_llm_settings_user_model",
        "llm_settings",
        ["user_id", "model"],
        unique=False,
    )
    op.create_table(
        "mcp_settings",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("user_id", sa.String(), nullable=False),
        sa.Column("mcp_config", sqlite.JSON(), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "sessions",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("user_id", sa.String(), nullable=False),
        sa.Column("sandbox_id", sa.String(), nullable=True),
        sa.Column("llm_setting_id", sa.String(), nullable=True),
        sa.Column("name", sa.String(), nullable=True),
        sa.Column("status", sa.String(), nullable=True),
        sa.Column("agent_state_path", sa.String(), nullable=True),
        sa.Column("agent_type", sa.String(), nullable=True),
        sa.Column("state_storage_url", sa.String(), nullable=True),
        sa.Column("public_url", sa.String(), nullable=True),
        sa.Column("is_public", sa.Boolean(), nullable=True),
        sa.Column("parent_session_id", sa.String(), nullable=True),
        sa.Column("last_message_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["llm_setting_id"],
            ["llm_settings.id"],
        ),
        sa.ForeignKeyConstraint(
            ["parent_session_id"],
            ["sessions.id"],
        ),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("idx_sessions_created_at", "sessions", ["created_at"], unique=False)
    op.create_index("idx_sessions_status", "sessions", ["status"], unique=False)
    op.create_index("idx_sessions_user_id", "sessions", ["user_id"], unique=False)
    op.create_table(
        "events",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("session_id", sa.String(), nullable=False),
        sa.Column("type", sa.String(), nullable=False),
        sa.Column("content", sqlite.JSON(), nullable=False),
        sa.Column("source", sa.String(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(["session_id"], ["sessions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("idx_events_created_at", "events", ["created_at"], unique=False)
    op.create_index("idx_events_session_id", "events", ["session_id"], unique=False)
    op.create_index("idx_events_type", "events", ["type"], unique=False)
    op.create_table(
        "file_uploads",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("user_id", sa.String(), nullable=False),
        sa.Column("file_name", sa.String(), nullable=False),
        sa.Column("file_size", sa.BigInteger(), nullable=False),
        sa.Column("storage_path", sa.String(), nullable=False),
        sa.Column("content_type", sa.String(), nullable=True),
        sa.Column("session_id", sa.String(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(["session_id"], ["sessions.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "session_metrics",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("session_id", sa.String(), nullable=False),
        sa.Column("credits", sa.Float(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(["session_id"], ["sessions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("session_id"),
    )
    op.create_index(
        "idx_session_metrics_session_id",
        "session_metrics",
        ["session_id"],
        unique=False,
    )
    op.create_index(
        "idx_session_metrics_updated_at",
        "session_metrics",
        ["updated_at"],
        unique=False,
    )
    op.create_table(
        "session_wishlists",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("user_id", sa.String(), nullable=False),
        sa.Column("session_id", sa.String(), nullable=False),
        sa.Column("created_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(["session_id"], ["sessions.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_session_wishlists_user_session",
        "session_wishlists",
        ["user_id", "session_id"],
        unique=True,
    )
    op.create_table(
        "slide_contents",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("session_id", sa.String(), nullable=False),
        sa.Column("presentation_name", sa.String(), nullable=False),
        sa.Column("slide_number", sa.BigInteger(), nullable=False),
        sa.Column("slide_title", sa.String(), nullable=True),
        sa.Column("slide_content", sa.String(), nullable=False),
        sa.Column("metadata", sqlite.JSON(), nullable=True),
        sa.Column("created_at", sa.TIMESTAMP(), nullable=True),
        sa.Column("updated_at", sa.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(["session_id"], ["sessions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_slide_contents_presentation_name",
        "slide_contents",
        ["presentation_name"],
        unique=False,
    )
    op.create_index(
        "idx_slide_contents_session_id", "slide_contents", ["session_id"], unique=False
    )
    op.create_index(
        "idx_slide_contents_session_presentation_slide",
        "slide_contents",
        ["session_id", "presentation_name", "slide_number"],
        unique=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        "idx_slide_contents_session_presentation_slide", table_name="slide_contents"
    )
    op.drop_index("idx_slide_contents_session_id", table_name="slide_contents")
    op.drop_index("idx_slide_contents_presentation_name", table_name="slide_contents")
    op.drop_table("slide_contents")
    op.drop_index("idx_session_wishlists_user_session", table_name="session_wishlists")
    op.drop_table("session_wishlists")
    op.drop_index("idx_session_metrics_updated_at", table_name="session_metrics")
    op.drop_index("idx_session_metrics_session_id", table_name="session_metrics")
    op.drop_table("session_metrics")
    op.drop_table("file_uploads")
    op.drop_index("idx_events_type", table_name="events")
    op.drop_index("idx_events_session_id", table_name="events")
    op.drop_index("idx_events_created_at", table_name="events")
    op.drop_table("events")
    op.drop_index("idx_sessions_user_id", table_name="sessions")
    op.drop_index("idx_sessions_status", table_name="sessions")
    op.drop_index("idx_sessions_created_at", table_name="sessions")
    op.drop_table("sessions")
    op.drop_table("mcp_settings")
    op.drop_index("idx_llm_settings_user_model", table_name="llm_settings")
    op.drop_table("llm_settings")
    op.drop_index("idx_users_email", table_name="users")
    op.drop_table("users")
    # ### end Alembic commands ###
