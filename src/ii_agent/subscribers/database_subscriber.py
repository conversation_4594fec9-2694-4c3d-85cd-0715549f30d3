import logging
import uuid
from typing import Optional

from ii_agent.core.event import Realtime<PERSON><PERSON>, EventType
from ii_agent.db.manager import Events, get_db
from ii_tool.tools.slide_system.slide_edit_tool import SlideEditTool
from ii_tool.tools.slide_system.slide_write_tool import SlideWriteTool


class DatabaseSubscriber:
    """Subscriber that handles database storage for events."""

    def __init__(self, session_id: Optional[uuid.UUID], logger: logging.Logger = None):
        self.session_id = session_id
        self._logger = logger or logging.getLogger(__name__)

    async def handle_event(self, event: RealtimeEvent) -> None:
        """Handle an event by saving it to the database."""
        # Save all events to database if we have a session
        if self.session_id is not None:
            # Handle file URLs from image/video generation tools
            if event.type == EventType.TOOL_RESULT:
                tool_result = event.content.get("result", {})
                tool_name = event.content.get("tool_name", "")

                # Special handling for file_url type results (image/video generation)
                if (
                    isinstance(tool_result, dict)
                    and tool_result.get("type") == "file_url"
                ):
                    # Import here to avoid circular imports
                    from ii_agent.server.shared import file_service

                    file_data = await file_service.write_file_from_url(
                        url=tool_result["url"],
                        file_name=tool_result["name"],
                        file_size=tool_result["size"],
                        content_type=tool_result["mime_type"],
                        session_id=str(self.session_id),
                    )
                    event.content["result"]["file_id"] = file_data.id
                    event.content["result"]["file_storage_path"] = (
                        file_data.storage_path
                    )

                # Special handling for slide tool results
                if tool_name in [SlideWriteTool.name, SlideEditTool.name]:
                    self._logger.info(
                        f"Handling slide tool result for tool: {tool_name}"
                    )
                    await self._handle_slide_tool_result(event, tool_name)

                # All tool results (including non-file results) are saved with the event

            await Events.save_event(self.session_id, event)
        else:
            self._logger.info(f"No session ID, skipping event: {event}")

    async def _handle_slide_tool_result(
        self, event: RealtimeEvent, tool_name: str
    ) -> None:
        """Handle slide tool results and save to database."""
        try:
            # Import here to avoid circular imports
            from ii_agent.server.slides.service import _save_slide_to_db

            tool_input = event.content.get("tool_input", {})
            tool_result = event.content.get("result", {})

            # Extract presentation info from tool input
            presentation_name = tool_input.get("presentation_name")
            slide_number = tool_input.get("slide_number")

            if not presentation_name or not slide_number:
                self._logger.warning(f"Missing presentation info in {tool_name} result")
                return

            # Get slide content based on tool type
            slide_content = None
            slide_title = ""

            # user_display = tool_result if isinstance(tool_result, dict) else
            if isinstance(tool_result, dict):
                user_display = tool_result
            elif isinstance(tool_result, list) and len(tool_result) > 0:
                user_display = tool_result[0]
            else:
                user_display = {}

            if tool_name == SlideWriteTool.name:
                if isinstance(user_display, dict):
                    slide_content = user_display.get("content", "")
                slide_title = tool_input.get("title", "")

            elif tool_name == SlideEditTool.name:
                slide_content = user_display.get("new_content", "")
                slide_title = tool_input.get("title", "")

            if not slide_content:
                self._logger.warning(f"No content found in {tool_name} result")
                return

            # Save to database using the service function
            async with get_db() as db_session:
                await _save_slide_to_db(
                    db_session=db_session,
                    session_id=str(self.session_id),
                    presentation_name=presentation_name,
                    slide_number=slide_number,
                    slide_title=slide_title,
                    slide_content=slide_content,
                    tool_name=tool_name,
                )
                self._logger.info(
                    f"Saved {tool_name} result for slide {slide_number} in {presentation_name}"
                )

        except Exception as e:
            self._logger.error(f"Error handling {tool_name} result: {e}", exc_info=True)

    def update_session_id(self, session_id: Optional[uuid.UUID]) -> None:
        """Update the session ID."""
        self.session_id = session_id
