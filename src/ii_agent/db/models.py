from datetime import datetime, timezone
import uuid
from sqlalchemy import (
    Column,
    String,
    ForeignKey,
    Boolean,
    Index,
    TIMESTAMP,
    BigInteger,
    Float,
)
from sqlalchemy.orm import relationship, declarative_base
from sqlalchemy.dialects.sqlite import JSON as SQLiteJSON

Base = declarative_base()


class User(Base):
    """Database model for users."""

    __tablename__ = "users"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String, unique=True, nullable=False)
    password_hash = Column(String, nullable=True)
    first_name = Column(String, nullable=True)
    last_name = Column(String, nullable=True)
    avatar = Column(String, nullable=True)
    role = Column(String, default="user")
    is_active = Column(Boolean, default=True)
    email_verified = Column(Boolean, default=False)
    created_at = Column(TIMESTAMP, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(
        TIMESTAMP,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
    )
    last_login_at = Column(TIMESTAMP, nullable=True)
    user_metadata = Column("metadata", SQLiteJSON, nullable=True)
    login_provider = Column(String, nullable=True)
    organization = Column(String, nullable=True)
    credits = Column(Float, nullable=False)  # Default will be set at service layer

    # Relationships
    sessions = relationship(
        "Session", back_populates="user", cascade="all, delete-orphan"
    )
    llm_settings = relationship(
        "LLMSetting", back_populates="user", cascade="all, delete-orphan"
    )
    mcp_settings = relationship(
        "MCPSetting", back_populates="user", cascade="all, delete-orphan"
    )
    file_uploads = relationship(
        "FileUpload", back_populates="user", cascade="all, delete-orphan"
    )
    session_wishlists = relationship(
        "SessionWishlist", back_populates="user", cascade="all, delete-orphan"
    )

    # Add index for email lookup
    __table_args__ = (Index("idx_users_email", "email"),)


class LLMSetting(Base):
    """Database model for LLM model settings."""

    __tablename__ = "llm_settings"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    model = Column(
        String, nullable=False
    )  # Model name (e.g., 'gpt-4', 'claude-3-opus')
    api_type = Column(String, nullable=False)  # 'openai', 'anthropic', 'gemini'
    encrypted_api_key = Column(String, nullable=True)
    base_url = Column(String, nullable=True)
    max_retries = Column(BigInteger, default=10)
    max_message_chars = Column(BigInteger, default=30000)
    temperature = Column(Float, default=0.0)
    thinking_tokens = Column(BigInteger, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(TIMESTAMP, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(
        TIMESTAMP,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
    )
    llm_metadata = Column(
        "metadata", SQLiteJSON, nullable=True
    )  # For Azure deployment names, Bedrock config, vertex settings, etc.

    # Relationships
    user = relationship("User", back_populates="llm_settings")
    sessions = relationship("Session", back_populates="llm_setting")

    # Add index for model lookup
    # Create a composite index on user_id and model columns for efficient lookups
    # This allows fast queries when filtering LLM settings by both user and model
    __table_args__ = (Index("idx_llm_settings_user_model", "user_id", "model"),)


class MCPSetting(Base):
    """Database model for MCP (Model Context Protocol) settings."""

    __tablename__ = "mcp_settings"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    mcp_config = Column(SQLiteJSON, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(TIMESTAMP, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(
        TIMESTAMP,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
    )

    # Relationships
    user = relationship("User", back_populates="mcp_settings")


class Session(Base):
    """Database model for agent sessions."""

    __tablename__ = "sessions"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    sandbox_id = Column(String, nullable=True)
    llm_setting_id = Column(String, ForeignKey("llm_settings.id"), nullable=True)
    name = Column(String, nullable=True)
    status = Column(String, default="active")  # 'pending', 'active', 'pause'
    agent_state_path = Column(String, nullable=True)  # Path to agent state storage
    agent_type = Column(
        String, default="general"
    )  # 'general', 'video_generate', 'image', 'slide', 'website_build'
    state_storage_url = Column(String, nullable=True)  # URL for state storage
    public_url = Column(String, nullable=True)
    is_public = Column(Boolean, default=False)

    # Parent session ID
    parent_session_id = Column(String, ForeignKey("sessions.id"), nullable=True)

    # Timestamps
    last_message_at = Column(TIMESTAMP, nullable=True)
    created_at = Column(TIMESTAMP, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(
        TIMESTAMP,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
    )
    deleted_at = Column(TIMESTAMP, nullable=True)

    # Relationships
    user = relationship("User", back_populates="sessions")
    llm_setting = relationship("LLMSetting", back_populates="sessions")
    events = relationship(
        "Event", back_populates="session", cascade="all, delete-orphan"
    )
    file_uploads = relationship(
        "FileUpload", back_populates="session", cascade="all, delete-orphan"
    )
    slide_contents = relationship(
        "SlideContent", back_populates="session", cascade="all, delete-orphan"
    )
    wishlisted_by = relationship(
        "SessionWishlist", back_populates="session", cascade="all, delete-orphan"
    )

    # Add indexes
    __table_args__ = (
        Index("idx_sessions_user_id", "user_id"),
        Index("idx_sessions_status", "status"),
        Index("idx_sessions_created_at", "created_at"),
    )


class Event(Base):
    """Database model for session events."""

    __tablename__ = "events"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(
        String, ForeignKey("sessions.id", ondelete="CASCADE"), nullable=False
    )
    type = Column(String, nullable=False)
    content = Column(SQLiteJSON, nullable=False)
    source = Column(String, nullable=True)
    created_at = Column(TIMESTAMP, default=lambda: datetime.now(timezone.utc))

    # Relationships
    session = relationship("Session", back_populates="events")

    # Add indexes
    __table_args__ = (
        Index("idx_events_session_id", "session_id"),
        Index("idx_events_created_at", "created_at"),
        Index("idx_events_type", "type"),
    )


class FileUpload(Base):
    """Database model for file uploads."""

    __tablename__ = "file_uploads"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    file_name = Column(String, nullable=False)
    file_size = Column(BigInteger, nullable=False)
    storage_path = Column(String, nullable=False)
    content_type = Column(String, nullable=True)
    session_id = Column(
        String, ForeignKey("sessions.id", ondelete="CASCADE"), nullable=True
    )
    created_at = Column(TIMESTAMP, default=lambda: datetime.now(timezone.utc))

    # Relationships
    user = relationship("User", back_populates="file_uploads")
    session = relationship("Session", back_populates="file_uploads")


class SlideContent(Base):
    """Database model for slide content storage."""

    __tablename__ = "slide_contents"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(
        String, ForeignKey("sessions.id", ondelete="CASCADE"), nullable=False
    )
    presentation_name = Column(String, nullable=False)
    slide_number = Column(BigInteger, nullable=False)
    slide_title = Column(String, nullable=True)
    slide_content = Column(String, nullable=False)  # Store HTML content as string
    slide_metadata = Column(
        "metadata", SQLiteJSON, nullable=True
    )  # Additional metadata
    created_at = Column(TIMESTAMP, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(
        TIMESTAMP,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
    )

    # Relationships
    session = relationship("Session", back_populates="slide_contents")

    # Add indexes for efficient queries
    __table_args__ = (
        Index("idx_slide_contents_session_id", "session_id"),
        Index("idx_slide_contents_presentation_name", "presentation_name"),
        Index(
            "idx_slide_contents_session_presentation_slide",
            "session_id",
            "presentation_name",
            "slide_number",
            unique=True,  # Ensure uniqueness of slide within session and presentation
        ),
    )


class SessionWishlist(Base):
    """Database model for session wishlists."""

    __tablename__ = "session_wishlists"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    session_id = Column(
        String, ForeignKey("sessions.id", ondelete="CASCADE"), nullable=False
    )
    created_at = Column(TIMESTAMP, default=lambda: datetime.now(timezone.utc))

    # Relationships
    user = relationship("User", back_populates="session_wishlists")
    session = relationship("Session", back_populates="wishlisted_by")

    # Add composite unique index to prevent duplicate wishlist entries
    __table_args__ = (
        Index(
            "idx_session_wishlists_user_session", "user_id", "session_id", unique=True
        ),
    )


class SessionMetrics(Base):
    """Database model for session-level credits tracking."""

    __tablename__ = "session_metrics"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(
        String,
        ForeignKey("sessions.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
    )

    # Credits tracking
    credits = Column(Float, default=0.0)

    # Timestamps
    created_at = Column(TIMESTAMP, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(
        TIMESTAMP,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
    )

    # Relationships
    session = relationship("Session", backref="metrics", uselist=False)

    # Add indexes for efficient queries
    __table_args__ = (
        Index("idx_session_metrics_session_id", "session_id"),
        Index("idx_session_metrics_updated_at", "updated_at"),
    )
