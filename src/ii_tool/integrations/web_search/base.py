from abc import ABC, abstractmethod
from typing import List, Dict


class BaseWebSearchClient(ABC):
    """Base interface for web search clients."""
    
    @abstractmethod
    async def search(self, query: str, max_results: int = 10) -> List[Dict[str, str]]:
        pass

    @abstractmethod
    async def batch_search(self, queries: List[str], max_results: int = 10) -> List[List[Dict[str, str]]]:
        pass