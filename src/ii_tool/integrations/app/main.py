import logging
import uvicorn
import argparse
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import Literal, List, Dict, Any

from .config import config
from .services import image_search_service, web_visit_service, video_generation_service
from ii_tool.integrations.image_generation import create_image_generation_client
from ii_tool.integrations.web_search import create_web_search_client
from ii_tool.integrations.web_search.exception import (
    WebSearchExhaustedError, 
    WebSearchProviderError,
    WebSearchNetworkError
)
from ii_tool.integrations.web_visit import create_web_visit_client
from ii_tool.integrations.database import create_database_client
from ii_agent.integrations.enhance_prompt import create_enhance_prompt_client


app = FastAPI()

# Configure logging
logging.basicConfig(
    level=logging.INFO,  # Minimum log level to capture
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
# Create a logger
logger = logging.getLogger("ii_tool")
# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- Health Check ---

@app.get("/health")
async def health_check():
    return {"status": "ok"}


# --- Image Generation ---

class ImageGenerationRequest(BaseModel):
    prompt: str
    aspect_ratio: Literal["1:1", "16:9", "9:16", "4:3", "3:4"] = "1:1"

class ImageGenerationResponse(BaseModel):
    success: bool
    url: str | None = None
    error: str | None = None
    size: int | None = None
    mime_type: str | None = None

@app.post("/image-generation", response_model=ImageGenerationResponse)
async def generate_image(
    request: ImageGenerationRequest,
    ):
    """Generate image from text prompt."""
    try: 
        client = create_image_generation_client(config.image_generate_config)
        image_result = await client.generate_image(
            prompt=request.prompt,
            aspect_ratio=request.aspect_ratio
        )
    except Exception as e:
        logger.error(f"Failed to generate image: {e}")
        return ImageGenerationResponse(success=False,  error=str(e))

    return ImageGenerationResponse(
        success= True,
        url=image_result.url,
        size=image_result.size,
        mime_type=image_result.mime_type,
    )


# --- Video Generation ---

class VideoGenerationRequest(BaseModel):
    prompt: str
    aspect_ratio: Literal["16:9", "9:16"] = "16:9"
    duration_seconds: int = Field(..., ge=5, le=30)
    image_base64: str | None = None
    image_mime_type: Literal["image/png", "image/jpeg", "image/webp"] | None = None

class VideoGenerationResponse(BaseModel):
    success: bool
    url: str | None = None
    size: int | None = None
    mime_type: str | None = None
    error: str | None = None

@app.post("/video-generation", response_model=VideoGenerationResponse)
async def video_generation(
    request: VideoGenerationRequest,
    ):
    """Generate video from text prompt or/and image."""
 
    video_result = await video_generation_service.generate_video(
        prompt=request.prompt,
        aspect_ratio=request.aspect_ratio,
        duration_seconds=request.duration_seconds,
        image_base64=request.image_base64,
        image_mime_type=request.image_mime_type,
    )

    return VideoGenerationResponse(success=True, url=video_result.url, size=video_result.size, mime_type=video_result.mime_type)


# --- Web Search ---

class WebSearchRequest(BaseModel):
    query: str
    max_results: int = 5

class WebSearchResponse(BaseModel):
    success: bool
    results: List[Dict[str, Any]] | None = None
    error: str | None = None

@app.post("/web-search", response_model=WebSearchResponse)
async def web_search(request: WebSearchRequest):
    """Perform web search using configured providers."""
    try: 
        client = create_web_search_client(config.web_search_config)
        results = await client.search(request.query, request.max_results)
        
        # Always return success=True if we got here, even with empty results
        return WebSearchResponse(success=True, results=results)
    except WebSearchExhaustedError as e:
        logger.error(str(e))
        raise HTTPException(status_code=429, detail=str(e))
    except (WebSearchNetworkError, WebSearchProviderError) as e:
        logger.error(str(e))
        raise HTTPException(status_code=503, detail=str(e)) 
    except Exception as e:
        logger.exception(f"Unexpected error in web search: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

class WebBatchSearchRequest(BaseModel):
    queries: List[str]
    max_results: int = 6
class WebBatchSearchResponse(BaseModel):
    success: bool
    results: List[List[Dict[str, Any]]] | None = None
    error: str | None = None

@app.post("/v2/web-search", response_model=WebBatchSearchResponse)
async def web_batch_search(request: WebBatchSearchRequest):
    """Perform web search using configured providers."""
    try: 
        client = create_web_search_client(config.web_search_config)
        results = await client.batch_search(request.queries, request.max_results)
        return WebBatchSearchResponse(success=True, results=results)
    except WebSearchExhaustedError as e:
        logger.error(str(e))
        raise HTTPException(status_code=429, detail=str(e))
    except (WebSearchNetworkError, WebSearchProviderError) as e:
        logger.error(str(e))
        raise HTTPException(status_code=503, detail=str(e)) 
    except Exception as e:
        logger.exception(f"Unexpected error in web search: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# --- Image Search ---

class ImageSearchRequest(BaseModel):
    query: str
    aspect_ratio: Literal["all", "square", "tall", "wide", "panoramic"] = "all"
    image_type: Literal["all", "face", "photo", "clipart", "lineart", "animated"] = "all"
    min_width: int = 0
    min_height: int = 0
    is_product: bool = False
    max_results: int = 5

class ImageSearchResponse(BaseModel):
    success: bool
    results: List[Dict[str, Any]] | None = None
    error: str | None = None

@app.post("/image-search", response_model=ImageSearchResponse)
async def image_search(request: ImageSearchRequest):
    """Perform image search using configured providers.""" 
    results = await image_search_service.search(
        query=request.query,
        aspect_ratio=request.aspect_ratio,
        image_type=request.image_type,
        min_width=request.min_width,
        min_height=request.min_height,
        is_product=request.is_product,
        max_results=request.max_results,
    )
    return ImageSearchResponse(success=True, results=results)


# --- Web Visit ---

class WebVisitRequest(BaseModel):
    url: str
    prompt: str | None = None

class WebVisitResponse(BaseModel):
    success: bool
    content: str | None = None
    error: str | None = None

@app.post("/web-visit", response_model=WebVisitResponse)
async def web_visit(request: WebVisitRequest):
    """Visit a web page and extract content."""
    try: 
        content = await web_visit_service.visit(request.url, request.prompt)
    except Exception as e:
        logger.error(f"Failed to visit web page: {e}")
        return WebVisitResponse(success=False, error=str(e))
    return WebVisitResponse(success=True, content=content)

class WebVisitCompressV2Request(BaseModel):
    urls: List[str]
    query: str

class WebVisitCompressV2Response(BaseModel):
    success: bool
    error: str | None
    content: str

@app.post("/v2/web-visit-compress", response_model=WebVisitCompressV2Response)
async def web_visit_compress_v2(request: WebVisitCompressV2Request):
    """Visit a web page and extract content."""
    client = create_web_visit_client(config.web_visit_config, config.compressor_config)
    try:
        content = await client.batch_extract_compress(request.urls, request.query)
        return WebVisitCompressV2Response(content=content, success=True, error=None)
    except Exception as e:
        logger.error(f"Failed to visit web page: {e}")
        return WebVisitCompressV2Response(content="", success=False, error=str(e))

class WebVisitCompressRequest(BaseModel):
    url: str
    query: str

class WebVisitCompressResponse(BaseModel):
    success: bool
    error: str | None
    content: str

@app.post("/web-visit-compress", response_model=WebVisitCompressResponse)
async def web_visit_compress(request: WebVisitCompressRequest):
    """Visit a web page and extract content."""
    client = create_web_visit_client(config.web_visit_config, config.compressor_config)
    try:
        content = await client.extract_compress(request.url, request.query)
        return WebVisitCompressResponse(content=content, success=True, error=None)
    except Exception as e:
        logger.error(f"Failed to visit web page: {e}")
        return WebVisitCompressResponse(content="", success=False, error=str(e))

class WebVisitCompressV3Request(BaseModel):
    urls: List[str]
    query: str

class WebVisitCompressV3Response(BaseModel):
    success: bool
    error: str | None
    content: str

@app.post("/v3/web-visit-compress", response_model=WebVisitCompressV3Response)
async def web_visit_compress_v3(request: WebVisitCompressV3Request):
    """Visit a web page and extract content using specified client type."""
    try:
        client = create_web_visit_client(config.web_visit_config, config.compressor_config, client_type="gemini")
        content = await client.batch_extract_compress(request.urls, request.query)
        return WebVisitCompressV3Response(content=content, success=True, error=None)
    except Exception as e:
        logger.error(f"Failed to visit web page with gemini: {e}")
        return WebVisitCompressV3Response(content="", success=False, error=str(e))

class DatabaseConnectionRequest(BaseModel):
    database_type: Literal["postgres"]

class DatabaseConnectionResponse(BaseModel):
    success: bool
    connection_string: str | None = None
    error: str | None = None

@app.post("/database", response_model=DatabaseConnectionResponse)
async def database_connection(request: DatabaseConnectionRequest):
    """Get a database connection."""
    try: 
        client = create_database_client(request.database_type, config.database_config)
        connection_string = client.get_database_connection()
    except Exception as e:
        logger.error(f"Failed to get database connection: {e}")
        return DatabaseConnectionResponse(success=False, error=str(e))
    return DatabaseConnectionResponse(success=True, connection_string=connection_string)


# --- Enhance Prompt ---

class EnhancePromptRequest(BaseModel):
    prompt: str
    context: str | None = None

class EnhancePromptResponse(BaseModel):
    original_prompt: str
    enhanced_prompt: str
    reasoning: str | None = None

@app.post("/enhance-prompt", response_model=EnhancePromptResponse)
async def enhance_prompt(request: EnhancePromptRequest):
    """Enhance a prompt for better AI responses."""
    client = create_enhance_prompt_client(config.enhance_prompt_config)
    if not client:
        # If no client configured, return the original prompt
        return EnhancePromptResponse(
            original_prompt=request.prompt,
            enhanced_prompt=request.prompt,
            reasoning="No enhance prompt provider configured"
        )
    
    result = await client.enhance(request.prompt, request.context)
    return EnhancePromptResponse(
        original_prompt=result.original_prompt,
        enhanced_prompt=result.enhanced_prompt,
        reasoning=result.reasoning
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run the tool server")
    parser.add_argument("--port", type=int, default=7000, help="Port to run the server on")
    args = parser.parse_args()

    uvicorn.run(app, host="0.0.0.0", port=args.port)