from abc import ABC, abstractmethod
from ii_tool.integrations.llm.config import LLMConfig
from openai import AsyncOpenAI
from typing import Literal


class LLMClient(ABC):
    def __init__(self, llm_config: LLMConfig):
        self.model_name = llm_config.openai_model
        self.llm_client = AsyncOpenAI(
            api_key=llm_config.openai_api_key,
        )
        if llm_config.openai_base_url:
            self.llm_client.base_url = llm_config.openai_base_url

    async def generate(
            self, 
            prompt: str, 
            reasoning_effort: Literal["low", "medium", "high"] = "low", 
            temperature: float | None = None, 
            max_output_tokens: int = 4096
        ) -> str:
        if self.model_name.startswith("gpt-5"):
            response = await self.llm_client.responses.create(
                model=self.model_name,
                input=prompt,
                reasoning={"effort": reasoning_effort},
                temperature=temperature,
                max_output_tokens=max_output_tokens,
                )
            return response.output_text
        else:
            response = await self.llm_client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "user", "content": prompt},
                ],
                temperature=temperature,
                max_completion_tokens=max_output_tokens,
            )
            return response.choices[0].message.content