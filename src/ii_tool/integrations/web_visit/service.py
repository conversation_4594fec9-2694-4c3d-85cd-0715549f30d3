from openai import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import utils
from .base import BaseWebVisitClient

class WebVisitService:
    def __init__(self, web_visit_client: BaseWebVisitClient, openai_api_key: str, openai_model: str, openai_base_url: str | None = None):
        self.web_visit_client = web_visit_client
        self.llm_client = AsyncOpenAI(
            api_key=openai_api_key,
        )
        if openai_base_url:
            self.llm_client.base_url = openai_base_url
        self.llm_model = openai_model

    async def visit(self, url: str, prompt: str | None = None) -> str:
        raw_content = await self.web_visit_client.extract(url)
        if not prompt:
            return raw_content

        # process the content with prompt
        formatted_prompt = utils.get_visit_webpage_prompt(raw_content, prompt)
        if self.llm_model.startswith("gpt-5"): # TODO: make this flexible
            response = await self.llm_client.responses.create(
                model=self.llm_model,
                input=formatted_prompt,
                reasoning={ "effort": "low" }, # The process information task don't require high effort
                max_output_tokens=4096,
                )
            return response.output_text
        else:
            response = await self.llm_client.chat.completions.create(
                model=self.llm_model,
                messages=[
                    {"role": "user", "content": formatted_prompt},
                ],
                temperature=0.7, # TODO: make it configurable
                max_tokens=4096,
            )
            return response.choices[0].message.content